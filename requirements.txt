# SocialVision: Advanced Facial Recognition Search Engine
# Python Dependencies for Academic Research Project
# Developer: Mihretab N. Afework (<EMAIL>)

# Core Web Framework
streamlit>=1.28.0
gradio>=3.50.0

# Computer Vision and Machine Learning
opencv-python>=4.8.0
face-recognition>=1.3.0
dlib>=19.24.0
mediapipe>=0.10.0
tensorflow>=2.13.0
torch>=2.0.0
torchvision>=0.15.0

# Data Processing and Analysis
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.11.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Image Processing
Pillow>=10.0.0
imageio>=2.31.0
scikit-image>=0.21.0

# Firebase and Cloud Services
firebase-admin>=6.2.0
google-cloud-firestore>=2.11.0
google-cloud-storage>=2.10.0

# Web Scraping and HTTP
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.11.0
urllib3>=2.0.0

# Data Validation and Processing
pydantic>=2.0.0
python-dotenv>=1.0.0
python-multipart>=0.0.6

# API Development (Optional)
fastapi>=0.100.0
uvicorn>=0.23.0
flask>=2.3.0

# Testing and Quality Assurance
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Utilities
tqdm>=4.65.0
python-dateutil>=2.8.0
pytz>=2023.3
click>=8.1.0

# Development Tools
jupyter>=1.0.0
ipykernel>=6.25.0
notebook>=7.0.0

# Performance and Optimization
numba>=0.57.0
cython>=3.0.0

# Security and Authentication
cryptography>=41.0.0
PyJWT>=2.8.0

# Configuration Management
pyyaml>=6.0.1
toml>=0.10.2
configparser>=5.3.0

# Logging and Monitoring
loguru>=0.7.0
python-json-logger>=2.0.7

# Database Utilities
sqlalchemy>=2.0.0
pymongo>=4.4.0

# Async Support
aiohttp>=3.8.0
asyncio>=3.4.3

# Documentation
sphinx>=7.1.0
mkdocs>=1.5.0
mkdocs-material>=9.1.0

# Version Control Integration
gitpython>=3.1.0

# Environment Management
python-decouple>=3.8
